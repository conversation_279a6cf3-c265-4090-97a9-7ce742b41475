// Shop type configuration
// This file defines the valid shop types and their configurations

export const VALID_SHOP_TYPES = ['food', 'retail', 'service', 'entertainment'] as const;
export type ShopType = typeof VALID_SHOP_TYPES[number];

// Shop type configurations
export const SHOP_TYPE_CONFIG: Record<ShopType, {
  label: string;
  description: string;
  icon: string;
  defaultSlug: string;
}> = {
  food: {
    label: 'Food & Restaurants',
    description: 'Restaurants, cafes, and food establishments',
    icon: '🍽️',
    defaultSlug: 'restaurant',
  },
  retail: {
    label: 'Retail & Shopping',
    description: 'Stores, boutiques, and retail establishments',
    icon: '🛍️',
    defaultSlug: 'store',
  },
  service: {
    label: 'Services',
    description: 'Service providers and professional services',
    icon: '🔧',
    defaultSlug: 'service',
  },
  entertainment: {
    label: 'Entertainment',
    description: 'Entertainment venues and activities',
    icon: '🎭',
    defaultSlug: 'venue',
  },
};

// Helper functions
export const isValidShopType = (type: string): type is ShopType => {
  return VALID_SHOP_TYPES.includes(type as ShopType);
};

export const getShopTypeConfig = (type: ShopType) => {
  return SHOP_TYPE_CONFIG[type];
};

export const getDefaultShopType = (): ShopType => 'food';

// URL generation helpers
export const generateShopUrl = (shopType: ShopType, shopSlug: string): string => {
  return `/${shopType}/${shopSlug}`;
};

export const generateBranchUrl = (shopType: ShopType, shopSlug: string, branchSlug: string): string => {
  return `/${shopType}/${shopSlug}/${branchSlug}`;
};

export const generateBranchCartUrl = (shopType: ShopType, shopSlug: string, branchSlug: string): string => {
  return `/${shopType}/${shopSlug}/${branchSlug}/cart`;
};

export const generateBranchOrdersUrl = (shopType: ShopType, shopSlug: string, branchSlug: string): string => {
  return `/${shopType}/${shopSlug}/${branchSlug}/orders`;
};

export const generateBranchCheckoutUrl = (shopType: ShopType, shopSlug: string, branchSlug: string): string => {
  return `/${shopType}/${shopSlug}/${branchSlug}/checkout`;
};

// Legacy support - redirect to main branch
export const generateShopOrdersUrl = (shopType: ShopType, shopSlug: string): string => {
  return `/${shopType}/${shopSlug}/main/orders`;
};
