// Google Maps utility functions and configuration

import { Loader } from '@googlemaps/js-api-loader';

// Extend window interface for Google Maps
declare global {
  interface Window {
    google: typeof google;
  }
}

// Google Maps configuration
export const GOOGLE_MAPS_CONFIG = {
  apiKey: process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || '',
  version: 'weekly',
  libraries: ['places', 'geometry', 'drawing'] as const,
  language: 'en',
  region: 'TH', // Thailand
};

// Default map options factory
export const getDefaultMapOptions = (): google.maps.MapOptions => ({
  zoom: 15,
  center: { lat: 13.7563, lng: 100.5018 }, // Bangkok, Thailand
  mapTypeId: google.maps.MapTypeId.ROADMAP,
  disableDefaultUI: false,
  zoomControl: true,
  streetViewControl: true,
  fullscreenControl: true,
  mapTypeControl: false,
  styles: [
    {
      featureType: 'poi.business',
      stylers: [{ visibility: 'on' }],
    },
    {
      featureType: 'poi.attraction',
      stylers: [{ visibility: 'on' }],
    },
  ],
});

// Shop marker icon configuration
export const getShopMarkerIcon = (): google.maps.Icon => ({
  url: 'data:image/svg+xml;base64,' + btoa(`
    <svg width="32" height="40" viewBox="0 0 32 40" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M16 0C7.163 0 0 7.163 0 16c0 16 16 24 16 24s16-8 16-24C32 7.163 24.837 0 16 0z" fill="#e58219"/>
      <circle cx="16" cy="16" r="12" fill="#ffffff"/>
      <path d="M12 12h8v2h-8v-2zm0 3h8v2h-8v-2zm0 3h6v2h-6v-2z" fill="#e58219"/>
    </svg>
  `),
  scaledSize: new google.maps.Size(32, 40),
  anchor: new google.maps.Point(16, 40),
});

// User location marker icon
export const getUserMarkerIcon = (): google.maps.Icon => ({
  url: 'data:image/svg+xml;base64,' + btoa(`
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="12" cy="12" r="11" fill="#4285f4" stroke="#ffffff" stroke-width="2"/>
      <circle cx="12" cy="12" r="6" fill="#ffffff"/>
      <circle cx="12" cy="12" r="3" fill="#4285f4"/>
    </svg>
  `),
  scaledSize: new google.maps.Size(24, 24),
  anchor: new google.maps.Point(12, 12),
});

// Initialize Google Maps loader
let loaderInstance: Loader | null = null;

export const getGoogleMapsLoader = (): Loader => {
  if (!loaderInstance) {
    if (!GOOGLE_MAPS_CONFIG.apiKey) {
      throw new Error('Google Maps API key is not configured. Please set NEXT_PUBLIC_GOOGLE_MAPS_API_KEY environment variable.');
    }

    loaderInstance = new Loader(GOOGLE_MAPS_CONFIG);
  }
  return loaderInstance;
};

// Alternative direct loading method
const loadGoogleMapsScript = (): Promise<void> => {
  return new Promise((resolve, reject) => {
    // Check if already loaded
    if (typeof window !== 'undefined' && window.google && window.google.maps) {
      resolve();
      return;
    }

    // Create script element
    const script = document.createElement('script');
    script.src = `https://maps.googleapis.com/maps/api/js?key=${GOOGLE_MAPS_CONFIG.apiKey}&libraries=${GOOGLE_MAPS_CONFIG.libraries.join(',')}&v=${GOOGLE_MAPS_CONFIG.version}`;
    script.async = true;
    script.defer = true;

    script.onload = () => {
      console.log('Google Maps script loaded successfully');
      resolve();
    };

    script.onerror = (error) => {
      console.error('Failed to load Google Maps script:', error);
      reject(new Error('Failed to load Google Maps script'));
    };

    document.head.appendChild(script);
  });
};

// Load Google Maps API
export const loadGoogleMaps = async (): Promise<typeof google.maps> => {
  try {
    console.log('Starting Google Maps API load...');
    console.log('API Key:', GOOGLE_MAPS_CONFIG.apiKey ? `${GOOGLE_MAPS_CONFIG.apiKey.substring(0, 10)}...` : 'Not set');
    console.log('Config:', GOOGLE_MAPS_CONFIG);

    // Check if Google Maps is already loaded
    if (typeof window !== 'undefined' && window.google && window.google.maps && window.google.maps.Map) {
      console.log('Google Maps already loaded, returning existing instance');
      return window.google.maps;
    }

    // Try using the js-api-loader first
    try {
      const loader = getGoogleMapsLoader();
      console.log('Loader created:', loader);

      const maps = await loader.load();
      console.log('Google Maps loaded successfully via loader:', maps);
      console.log('Available maps properties:', Object.keys(maps));
      console.log('maps.Map constructor:', maps.Map);

      // Verify that Map constructor is available
      if (!maps.Map) {
        throw new Error('Google Maps API loaded but Map constructor is not available');
      }

      return maps;
    } catch (loaderError) {
      console.warn('js-api-loader failed, trying direct script loading:', loaderError);

      // Fallback to direct script loading
      await loadGoogleMapsScript();

      // Wait a bit for the API to initialize
      await new Promise(resolve => setTimeout(resolve, 100));

      if (typeof window !== 'undefined' && window.google && window.google.maps && window.google.maps.Map) {
        console.log('Google Maps loaded successfully via direct script');
        return window.google.maps;
      } else {
        throw new Error('Google Maps failed to load via direct script');
      }
    }
  } catch (error) {
    console.error('Failed to load Google Maps API:', error);
    throw error;
  }
};

// Geocoding utilities
export const geocodeAddress = async (address: string): Promise<google.maps.LatLng | null> => {
  try {
    const maps = await loadGoogleMaps();
    const geocoder = new maps.Geocoder();

    return new Promise((resolve, reject) => {
      geocoder.geocode({ address }, (results, status) => {
        if (status === 'OK' && results && results[0]) {
          resolve(results[0].geometry.location);
        } else {
          reject(new Error(`Geocoding failed: ${status}`));
        }
      });
    });
  } catch (error) {
    console.error('Geocoding error:', error);
    return null;
  }
};

// Reverse geocoding
export const reverseGeocode = async (lat: number, lng: number): Promise<string | null> => {
  try {
    const maps = await loadGoogleMaps();
    const geocoder = new maps.Geocoder();

    return new Promise((resolve, reject) => {
      geocoder.geocode({ location: { lat, lng } }, (results, status) => {
        if (status === 'OK' && results && results[0]) {
          resolve(results[0].formatted_address);
        } else {
          reject(new Error(`Reverse geocoding failed: ${status}`));
        }
      });
    });
  } catch (error) {
    console.error('Reverse geocoding error:', error);
    return null;
  }
};

// Calculate distance between two points
export const calculateDistance = (
  point1: { lat: number; lng: number },
  point2: { lat: number; lng: number }
): number => {
  const R = 6371; // Earth's radius in kilometers
  const dLat = (point2.lat - point1.lat) * (Math.PI / 180);
  const dLng = (point2.lng - point1.lng) * (Math.PI / 180);
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(point1.lat * (Math.PI / 180)) *
      Math.cos(point2.lat * (Math.PI / 180)) *
      Math.sin(dLng / 2) *
      Math.sin(dLng / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
};

// Get user's current location
export const getCurrentLocation = (): Promise<GeolocationPosition> => {
  return new Promise((resolve, reject) => {
    if (!navigator.geolocation) {
      reject(new Error('Geolocation is not supported by this browser.'));
      return;
    }

    navigator.geolocation.getCurrentPosition(
      (position) => resolve(position),
      (error) => reject(error),
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 300000, // 5 minutes
      }
    );
  });
};

// Format distance for display
export const formatDistance = (distance: number): string => {
  if (distance < 1) {
    return `${Math.round(distance * 1000)}m`;
  } else {
    return `${distance.toFixed(1)}km`;
  }
};

// Shop location interface
export interface ShopLocation {
  id: string;
  name: string;
  description?: string;
  address: string;
  lat: number;
  lng: number;
  rating?: number;
  priceRange?: string;
  cuisineType?: string;
  isOpen?: boolean;
  distance?: number;
  image?: string;
  phone?: string;
  website?: string;
}

// Map bounds utility
export const fitMapToBounds = (
  map: google.maps.Map,
  locations: { lat: number; lng: number }[]
): void => {
  if (locations.length === 0) return;

  const bounds = new google.maps.LatLngBounds();
  locations.forEach((location) => {
    bounds.extend(new google.maps.LatLng(location.lat, location.lng));
  });

  map.fitBounds(bounds);

  // Ensure minimum zoom level
  const listener = google.maps.event.addListener(map, 'bounds_changed', () => {
    if (map.getZoom() && map.getZoom()! > 18) {
      map.setZoom(18);
    }
    google.maps.event.removeListener(listener);
  });
};

// Places search utility
export const searchNearbyPlaces = async (
  location: { lat: number; lng: number },
  radius: number = 5000,
  type: string = 'restaurant'
): Promise<google.maps.places.PlaceResult[]> => {
  try {
    const maps = await loadGoogleMaps();
    const service = new maps.places.PlacesService(document.createElement('div'));

    return new Promise((resolve, reject) => {
      service.nearbySearch(
        {
          location: new maps.LatLng(location.lat, location.lng),
          radius,
          type,
        },
        (results, status) => {
          if (status === maps.places.PlacesServiceStatus.OK && results) {
            resolve(results);
          } else {
            reject(new Error(`Places search failed: ${status}`));
          }
        }
      );
    });
  } catch (error) {
    console.error('Places search error:', error);
    return [];
  }
};
