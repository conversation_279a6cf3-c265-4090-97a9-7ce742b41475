// Cart Service for backend synchronization
// Handles both logged-in users and guest sessions

import { CartItem } from '@/lib/context/CartContext';

export interface CartSession {
  id: string;
  user_id?: string; // null for guest users
  session_id: string; // for guest users
  items: CartItem[];
  created_at: string;
  updated_at: string;
  expires_at: string;
}

export interface CartResponse {
  success: boolean;
  data?: CartSession;
  message?: string;
}

class CartService {
  private baseUrl = '/api/services/cart';
  private sessionId: string | null = null;

  constructor() {
    // Generate or retrieve session ID for guest users
    if (typeof window !== 'undefined') {
      this.sessionId = this.getOrCreateSessionId();
    }
  }

  private getOrCreateSessionId(): string {
    const stored = localStorage.getItem('cart_session_id');
    if (stored) {
      return stored;
    }
    
    // Generate new session ID
    const newSessionId = `guest_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    localStorage.setItem('cart_session_id', newSessionId);
    return newSessionId;
  }

  private async makeRequest(endpoint: string, options: RequestInit = {}): Promise<CartResponse> {
    try {
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        headers: {
          'Content-Type': 'application/json',
          'X-Session-ID': this.sessionId || '',
          ...options.headers,
        },
        ...options,
      });

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Cart service error:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Cart operation failed',
      };
    }
  }

  // Get cart from backend
  async getCart(): Promise<CartResponse> {
    return this.makeRequest('');
  }

  // Add item to cart
  async addToCart(item: Omit<CartItem, 'quantity'>, quantity: number = 1): Promise<CartResponse> {
    return this.makeRequest('/add', {
      method: 'POST',
      body: JSON.stringify({ item, quantity }),
    });
  }

  // Update item quantity
  async updateQuantity(itemId: string, quantity: number): Promise<CartResponse> {
    return this.makeRequest('/update', {
      method: 'PUT',
      body: JSON.stringify({ item_id: itemId, quantity }),
    });
  }

  // Remove item from cart
  async removeFromCart(itemId: string): Promise<CartResponse> {
    return this.makeRequest('/remove', {
      method: 'DELETE',
      body: JSON.stringify({ item_id: itemId }),
    });
  }

  // Clear entire cart
  async clearCart(): Promise<CartResponse> {
    return this.makeRequest('/clear', {
      method: 'DELETE',
    });
  }

  // Clear cart for specific branch
  async clearBranchCart(shopSlug: string, branchSlug: string): Promise<CartResponse> {
    return this.makeRequest('/clear-branch', {
      method: 'DELETE',
      body: JSON.stringify({ shop_slug: shopSlug, branch_slug: branchSlug }),
    });
  }

  // Sync cart when user logs in (merge guest cart with user cart)
  async syncCartOnLogin(): Promise<CartResponse> {
    return this.makeRequest('/sync', {
      method: 'POST',
    });
  }

  // Get branch-specific cart items
  getBranchItems(items: CartItem[], shopSlug: string, branchSlug: string): CartItem[] {
    return items.filter(item => 
      item.shopSlug === shopSlug && item.branchSlug === branchSlug
    );
  }

  // Calculate totals for branch
  getBranchTotals(items: CartItem[], shopSlug: string, branchSlug: string) {
    const branchItems = this.getBranchItems(items, shopSlug, branchSlug);
    const totalItems = branchItems.reduce((sum, item) => sum + item.quantity, 0);
    const totalPrice = branchItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    
    return { totalItems, totalPrice, items: branchItems };
  }

  // Clear session ID (for logout)
  clearSession(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('cart_session_id');
      this.sessionId = null;
    }
  }

  // Get current session ID
  getSessionId(): string | null {
    return this.sessionId;
  }
}

export const cartService = new CartService();
export default CartService;
