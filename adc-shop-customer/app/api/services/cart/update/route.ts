import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/nextauth.config';

const BACKEND_URL = process.env.CUSTOMER_BACKEND_URL || 'http://localhost:8900';

// PUT /api/services/cart/update - Update item quantity
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    const sessionId = request.headers.get('X-Session-ID');
    const body = await request.json();

    // Validate request body
    if (!body.item_id || typeof body.quantity !== 'number') {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Item ID and quantity are required',
          error: 'VALIDATION_ERROR'
        },
        { status: 400 }
      );
    }

    // Prepare headers for backend request
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    // Add authentication headers
    if (session?.user) {
      headers['X-User-ID'] = session.user.id;
      headers['X-User-Email'] = session.user.email;
      headers['X-User-Role'] = session.user.role || 'customer';
      headers['X-Auth-Source'] = 'nextauth';
    } else if (sessionId) {
      headers['X-Session-ID'] = sessionId;
    } else {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Session ID is required for guest users',
          error: 'AUTH_ERROR'
        },
        { status: 401 }
      );
    }

    // Make request to backend
    const response = await fetch(`${BACKEND_URL}/api/v1/cart/update`, {
      method: 'PUT',
      headers,
      body: JSON.stringify({
        item_id: body.item_id,
        quantity: body.quantity,
      }),
    });

    if (!response.ok) {
      console.error(`Backend request failed: ${response.status} ${response.statusText}`);
      return NextResponse.json(
        { 
          success: false, 
          message: `Failed to update cart item: ${response.statusText}`,
          error: 'BACKEND_ERROR'
        },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);

  } catch (error) {
    console.error('Error in PUT /api/services/cart/update:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Internal server error',
        error: 'INTERNAL_ERROR'
      },
      { status: 500 }
    );
  }
}
