import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/nextauth.config';

const BACKEND_URL = process.env.CUSTOMER_BACKEND_URL || 'http://localhost:8900';

// POST /api/services/cart/sync - Sync guest cart with user cart on login
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    const sessionId = request.headers.get('X-Session-ID');

    // This endpoint requires a logged-in user
    if (!session?.user) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'User must be logged in to sync cart',
          error: 'AUTH_ERROR'
        },
        { status: 401 }
      );
    }

    // Prepare headers for backend request
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      'X-User-ID': session.user.id,
      'X-User-Email': session.user.email,
      'X-User-Role': session.user.role || 'customer',
      'X-Auth-Source': 'nextauth',
    };

    // Include session ID if provided (for merging guest cart)
    if (sessionId) {
      headers['X-Session-ID'] = sessionId;
    }

    // Make request to backend
    const response = await fetch(`${BACKEND_URL}/api/v1/cart/sync`, {
      method: 'POST',
      headers,
    });

    if (!response.ok) {
      console.error(`Backend request failed: ${response.status} ${response.statusText}`);
      return NextResponse.json(
        { 
          success: false, 
          message: `Failed to sync cart: ${response.statusText}`,
          error: 'BACKEND_ERROR'
        },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);

  } catch (error) {
    console.error('Error in POST /api/services/cart/sync:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Internal server error',
        error: 'INTERNAL_ERROR'
      },
      { status: 500 }
    );
  }
}
