import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/nextauth.config';

const BACKEND_URL = process.env.CUSTOMER_BACKEND_URL || 'http://localhost:8900';

// GET /api/services/cart - Get user's cart
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    const sessionId = request.headers.get('X-Session-ID');

    // Prepare headers for backend request
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    // Add authentication headers
    if (session?.user) {
      headers['X-User-ID'] = session.user.id;
      headers['X-User-Email'] = session.user.email;
      headers['X-User-Role'] = session.user.role || 'customer';
      headers['X-Auth-Source'] = 'nextauth';
    } else if (sessionId) {
      headers['X-Session-ID'] = sessionId;
    }

    // Make request to backend
    const response = await fetch(`${BACKEND_URL}/api/v1/cart`, {
      method: 'GET',
      headers,
    });

    if (!response.ok) {
      console.error(`Backend request failed: ${response.status} ${response.statusText}`);
      return NextResponse.json(
        { 
          success: false, 
          message: `Failed to fetch cart: ${response.statusText}`,
          error: 'BACKEND_ERROR'
        },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);

  } catch (error) {
    console.error('Error in GET /api/services/cart:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Internal server error',
        error: 'INTERNAL_ERROR'
      },
      { status: 500 }
    );
  }
}

// POST /api/services/cart/add - Add item to cart
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    const sessionId = request.headers.get('X-Session-ID');
    const body = await request.json();

    // Prepare headers for backend request
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    // Add authentication headers
    if (session?.user) {
      headers['X-User-ID'] = session.user.id;
      headers['X-User-Email'] = session.user.email;
      headers['X-User-Role'] = session.user.role || 'customer';
      headers['X-Auth-Source'] = 'nextauth';
    } else if (sessionId) {
      headers['X-Session-ID'] = sessionId;
    }

    // Make request to backend
    const response = await fetch(`${BACKEND_URL}/api/v1/cart`, {
      method: 'POST',
      headers,
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      console.error(`Backend request failed: ${response.status} ${response.statusText}`);
      return NextResponse.json(
        { 
          success: false, 
          message: `Failed to add to cart: ${response.statusText}`,
          error: 'BACKEND_ERROR'
        },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);

  } catch (error) {
    console.error('Error in POST /api/services/cart:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Internal server error',
        error: 'INTERNAL_ERROR'
      },
      { status: 500 }
    );
  }
}

// DELETE /api/services/cart - Clear entire cart
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    const sessionId = request.headers.get('X-Session-ID');

    // Prepare headers for backend request
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    // Add authentication headers
    if (session?.user) {
      headers['X-User-ID'] = session.user.id;
      headers['X-User-Email'] = session.user.email;
      headers['X-User-Role'] = session.user.role || 'customer';
      headers['X-Auth-Source'] = 'nextauth';
    } else if (sessionId) {
      headers['X-Session-ID'] = sessionId;
    }

    // Make request to backend
    const response = await fetch(`${BACKEND_URL}/api/v1/cart`, {
      method: 'DELETE',
      headers,
    });

    if (!response.ok) {
      console.error(`Backend request failed: ${response.status} ${response.statusText}`);
      return NextResponse.json(
        { 
          success: false, 
          message: `Failed to clear cart: ${response.statusText}`,
          error: 'BACKEND_ERROR'
        },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);

  } catch (error) {
    console.error('Error in DELETE /api/services/cart:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Internal server error',
        error: 'INTERNAL_ERROR'
      },
      { status: 500 }
    );
  }
}
