import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/nextauth.config';

const BACKEND_URL = process.env.CUSTOMER_BACKEND_URL || 'http://localhost:8900';

// DELETE /api/services/cart/remove - Remove item from cart
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    const sessionId = request.headers.get('X-Session-ID');
    const body = await request.json();

    // Validate request body
    if (!body.item_id) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Item ID is required',
          error: 'VALIDATION_ERROR'
        },
        { status: 400 }
      );
    }

    // Prepare headers for backend request
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    // Add authentication headers
    if (session?.user) {
      headers['X-User-ID'] = session.user.id;
      headers['X-User-Email'] = session.user.email;
      headers['X-User-Role'] = session.user.role || 'customer';
      headers['X-Auth-Source'] = 'nextauth';
    } else if (sessionId) {
      headers['X-Session-ID'] = sessionId;
    } else {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Session ID is required for guest users',
          error: 'AUTH_ERROR'
        },
        { status: 401 }
      );
    }

    // Make request to backend
    const response = await fetch(`${BACKEND_URL}/api/v1/cart/remove`, {
      method: 'DELETE',
      headers,
      body: JSON.stringify({
        item_id: body.item_id,
      }),
    });

    if (!response.ok) {
      console.error(`Backend request failed: ${response.status} ${response.statusText}`);
      return NextResponse.json(
        { 
          success: false, 
          message: `Failed to remove cart item: ${response.statusText}`,
          error: 'BACKEND_ERROR'
        },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);

  } catch (error) {
    console.error('Error in DELETE /api/services/cart/remove:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Internal server error',
        error: 'INTERNAL_ERROR'
      },
      { status: 500 }
    );
  }
}
