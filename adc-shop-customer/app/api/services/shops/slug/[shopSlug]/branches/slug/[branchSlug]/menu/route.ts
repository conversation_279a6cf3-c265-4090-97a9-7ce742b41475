import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/nextauth.config';

const BACKEND_URL = process.env.CUSTOMER_BACKEND_URL || 'http://localhost:8900';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ shopSlug: string; branchSlug: string }> }
) {
  try {
    const resolvedParams = await params;
    const { shopSlug, branchSlug } = resolvedParams;
    const { searchParams } = new URL(request.url);

    // Get session for potential authentication
    const session = await getServerSession(authOptions);

    // Build the backend URL with query parameters
    const backendUrl = new URL(`/api/v1/shops/slug/${shopSlug}/branches/slug/${branchSlug}/menu`, BACKEND_URL);

    // Forward all query parameters
    searchParams.forEach((value, key) => {
      backendUrl.searchParams.append(key, value);
    });

    // Prepare headers for backend request
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    // Add authentication headers if session exists
    if (session?.user) {
      headers['X-User-ID'] = session.user.id;
      headers['X-User-Email'] = session.user.email;
      headers['X-User-Role'] = session.user.role || 'customer';
      headers['X-Auth-Source'] = 'nextauth';
    }

    // Make request to backend
    const response = await fetch(backendUrl.toString(), {
      method: 'GET',
      headers,
    });

    if (!response.ok) {
      console.error(`Backend request failed: ${response.status} ${response.statusText}`);
      return NextResponse.json(
        {
          success: false,
          message: `Failed to fetch menu items: ${response.statusText}`,
          error: 'BACKEND_ERROR'
        },
        { status: response.status }
      );
    }

    const data = await response.json();

    // Return the data from backend
    return NextResponse.json(data);

  } catch (error) {
    console.error('Error in branch menu API route:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Internal server error',
        error: 'INTERNAL_ERROR'
      },
      { status: 500 }
    );
  }
}
