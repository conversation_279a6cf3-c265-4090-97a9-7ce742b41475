// This is the new dynamic route structure for shop pages
// Supports multiple shop types like: /food/restaurant-name, /retail/store-name, etc.

import { notFound } from 'next/navigation';
import { VALID_SHOP_TYPES, ShopType, isValidShopType, getShopTypeConfig } from '@/lib/config/shop-types';
import ShopNavigation from '@/components/ShopNavigation';

interface ShopPageProps {
  params: Promise<{
    'shop-type': string;
    'shop-slug': string;
  }>;
}

export default async function ShopPage({ params }: ShopPageProps) {
  const resolvedParams = await params;
  const shopType = resolvedParams['shop-type'] as ShopType;
  const shopSlug = resolvedParams['shop-slug'];

  // Validate shop type
  if (!isValidShopType(shopType)) {
    notFound();
  }

  // For now, we'll import and use the existing food page component
  // In the future, we can create different components for different shop types
  const { default: FoodShopPage } = await import('../../food/[shop-slug]/page');

  // Create a wrapper component that includes shop navigation
  const ShopPageWithNavigation = () => (
    <div>
      <ShopNavigation
        shopType={shopType}
        shopSlug={shopSlug}
        // TODO: Fetch actual shop data and pass it here
        // shopName={shop?.name}
        // shopRating={shop?.rating}
        // shopAddress={shop?.address}
        // shopPhone={shop?.phone}
        // isOpen={shop?.is_open}
      />
      <FoodShopPage params={Promise.resolve({ 'shop-slug': shopSlug })} />
    </div>
  );

  return <ShopPageWithNavigation />;
}

// Generate metadata for the page
export async function generateMetadata({ params }: ShopPageProps) {
  const resolvedParams = await params;
  const shopType = resolvedParams['shop-type'] as ShopType;
  const shopSlug = resolvedParams['shop-slug'];

  if (!isValidShopType(shopType)) {
    return {
      title: 'Shop Not Found',
      description: 'The requested shop could not be found.',
    };
  }

  const config = getShopTypeConfig(shopType);
  const shopName = shopSlug.replace(/-/g, ' ');

  return {
    title: `${shopName} - ${config.label}`,
    description: `Visit ${shopName} for the best ${config.description.toLowerCase()}.`,
  };
}
