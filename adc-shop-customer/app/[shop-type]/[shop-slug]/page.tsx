// This page now redirects to the first branch or shows branch selection
// The actual menu is now at /[shop-type]/[shop-slug]/[branch-slug]/page.tsx

import { notFound, redirect } from 'next/navigation';
import { VALID_SHOP_TYPES, ShopType, isValidShopType, getShopTypeConfig } from '@/lib/config/shop-types';

interface ShopPageProps {
  params: Promise<{
    'shop-type': string;
    'shop-slug': string;
  }>;
}

export default async function ShopPage({ params }: ShopPageProps) {
  const resolvedParams = await params;
  const shopType = resolvedParams['shop-type'] as ShopType;
  const shopSlug = resolvedParams['shop-slug'];

  // Validate shop type
  if (!isValidShopType(shopType)) {
    notFound();
  }

  // TODO: Fetch shop data and branches from API
  // For now, redirect to a default branch slug
  // In a real implementation, you would:
  // 1. Fetch shop data to verify it exists
  // 2. Get the list of branches
  // 3. Either redirect to the first/main branch or show branch selection page

  // Temporary redirect to main branch - replace with actual branch fetching logic
  redirect(`/${shopType}/${shopSlug}/main`);
}

// Generate metadata for the page
export async function generateMetadata({ params }: ShopPageProps) {
  const resolvedParams = await params;
  const shopType = resolvedParams['shop-type'] as ShopType;
  const shopSlug = resolvedParams['shop-slug'];

  if (!isValidShopType(shopType)) {
    return {
      title: 'Shop Not Found',
      description: 'The requested shop could not be found.',
    };
  }

  const config = getShopTypeConfig(shopType);
  const shopName = shopSlug.replace(/-/g, ' ');

  return {
    title: `${shopName} - ${config.label}`,
    description: `Visit ${shopName} for the best ${config.description.toLowerCase()}.`,
  };
}
