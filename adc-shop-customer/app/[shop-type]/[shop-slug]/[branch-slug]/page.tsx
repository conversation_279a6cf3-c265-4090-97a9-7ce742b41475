"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { CenterPagination, PaginationInfo, PageSizeSelector } from "@/components/ui/pagination";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { List, Grid3X3, Search } from "lucide-react";
import Header from "@/components/Header";
import { useCart } from "@/lib/context/CartContext";
import { CustomerMenuItem, MenuFilters } from "@/lib/services/customerApiClient";
import { useFilteredPagination } from "@/lib/hooks/usePagination";
import { useGetMenuItemsByBranchSlugQuery } from "@/lib/store/api/customerApi";

interface BranchMenuPageProps {
  params: Promise<{
    "shop-type": string;
    "shop-slug": string;
    "branch-slug": string;
  }>;
}

export default function BranchMenuPage({ params }: BranchMenuPageProps) {
  const resolvedParams = React.use(params);
  const shopType = resolvedParams["shop-type"];
  const shopSlug = resolvedParams["shop-slug"];
  const branchSlug = resolvedParams["branch-slug"];
  const { addToCart } = useCart();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [viewMode, setViewMode] = useState<"list" | "grid">("list");

  // Initialize pagination with filters
  const pagination = useFilteredPagination<MenuFilters>({
    initialPage: 1,
    initialLimit: 20,
    initialFilters: {
      is_available: true,
    },
  });

  // Prepare filters for RTK Query - now includes branch context
  const filters: MenuFilters = {
    ...pagination.filters,
    page: pagination.currentPage,
    limit: pagination.limit,
    search: searchQuery || undefined,
    categories: selectedCategory !== "all" ? [selectedCategory] : undefined,
  };

  // Use RTK Query to fetch menu items with branch-specific API
  const {
    data: menuResponse,
    error,
    isLoading,
    refetch,
  } = useGetMenuItemsByBranchSlugQuery({
    shopSlug,
    branchSlug,
    filters,
  });

  // Extract data from response
  const menuItems = menuResponse?.data?.items || [];
  const categories = menuResponse?.data?.categories || [];

  // Update pagination info when data changes
  useEffect(() => {
    if (menuResponse?.pagination) {
      pagination.setPaginationInfo(menuResponse.pagination);
    }
  }, [menuResponse?.pagination, pagination]);

  // Handle search
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    pagination.setPage(1);
  };

  // Handle category change
  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
    pagination.setPage(1);
  };

  // Handle view mode change
  const handleViewModeChange = (mode: "list" | "grid") => {
    setViewMode(mode);
  };

  // Handle add to cart
  const handleAddToCart = (item: CustomerMenuItem) => {
    addToCart({
      id: item.id,
      name: item.name,
      description: item.description,
      image: item.image,
      price: item.price,
      // Add shop and branch context to cart items
      shopSlug,
      branchSlug,
    });
  };

  if (isLoading && menuItems.length === 0) {
    return (
      <div className="relative flex size-full min-h-screen flex-col bg-background">
        <div className="layout-container flex h-full grow flex-col">
          <Header shopType={shopType} shopSlug={shopSlug} branchSlug={branchSlug} />
          <div className="flex flex-1 justify-center items-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">Loading menu...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    const errorMessage = 'status' in error
      ? `Error ${error.status}: Failed to load menu items`
      : error.message || 'Failed to load menu items';

    return (
      <div className="relative flex size-full min-h-screen flex-col bg-background">
        <div className="layout-container flex h-full grow flex-col">
          <Header shopType={shopType} shopSlug={shopSlug} branchSlug={branchSlug} />
          <div className="flex flex-1 justify-center items-center">
            <div className="text-center">
              <p className="text-destructive mb-4">{errorMessage}</p>
              <Button onClick={() => refetch()}>Try Again</Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative flex size-full min-h-screen flex-col bg-background">
      <div className="layout-container flex h-full grow flex-col">
        <Header shopType={shopType} shopSlug={shopSlug} branchSlug={branchSlug} />

        {/* Main Content */}
        <div className="px-40 flex flex-1 justify-center py-5">
          <div className="layout-content-container flex flex-col max-w-[960px] flex-1">
            <h2 className="text-foreground tracking-light text-[28px] font-bold leading-tight px-4 text-left pb-3 pt-5">
              Restaurant Menu - {branchSlug.replace(/-/g, ' ')}
            </h2>

            {/* Search Bar */}
            <div className="px-4 pb-3">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Search menu items..."
                  value={searchQuery}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            {/* Filter Tags */}
            <div className="flex gap-3 p-3 flex-wrap pr-4">
              <Badge
                variant={selectedCategory === "all" ? "default" : "secondary"}
                className="h-8 px-4 cursor-pointer"
                onClick={() => handleCategoryChange("all")}
              >
                All
              </Badge>
              {categories.map((category) => (
                <Badge
                  key={category.id}
                  variant={selectedCategory === category.name ? "default" : "secondary"}
                  className="h-8 px-4 cursor-pointer"
                  onClick={() => handleCategoryChange(category.name)}
                >
                  {category.name} ({category.item_count})
                </Badge>
              ))}
            </div>

            {/* Controls */}
            <div className="flex justify-between items-center gap-2 px-4 py-3">
              {/* View Toggle */}
              <ToggleGroup
                type="single"
                value={viewMode}
                onValueChange={(value) => value && handleViewModeChange(value as "list" | "grid")}
              >
                <ToggleGroupItem value="list" aria-label="List view">
                  <List className="h-4 w-4" />
                </ToggleGroupItem>
                <ToggleGroupItem value="grid" aria-label="Grid view">
                  <Grid3X3 className="h-4 w-4" />
                </ToggleGroupItem>
              </ToggleGroup>

              {/* Page Size Selector */}
              <PageSizeSelector
                currentSize={pagination.limit}
                options={[10, 20, 50]}
                onSizeChange={pagination.setLimit}
              />
            </div>

            {/* Pagination Info */}
            <div className="px-4 pb-3">
              <PaginationInfo
                startItem={pagination.startItem}
                endItem={pagination.endItem}
                totalItems={pagination.totalItems}
              />
            </div>

            {/* Menu Items */}
            {menuItems.map((item) => (
              <div key={item.id} className="p-4">
                <Card className="overflow-hidden">
                  <div className="flex flex-col xl:flex-row">
                    <div
                      className="w-full xl:w-1/3 bg-center bg-no-repeat aspect-video bg-cover"
                      style={{ backgroundImage: `url("${item.image}")` }}
                    />
                    <div className="flex-1 p-6">
                      <CardHeader className="p-0 pb-4">
                        <Badge variant="secondary" className="w-fit mb-2">{item.category}</Badge>
                        <CardTitle className="text-lg">{item.name}</CardTitle>
                        <CardDescription className="text-base">
                          {item.description}
                        </CardDescription>
                      </CardHeader>
                      <CardFooter className="p-0 flex items-center justify-between">
                        <span className="text-foreground text-lg font-bold">${item.price.toFixed(2)}</span>
                        <Button
                          className="bg-secondary text-secondary-foreground hover:bg-secondary/80"
                          onClick={() => handleAddToCart(item)}
                          disabled={!item.is_available}
                        >
                          {item.is_available ? "Add to Cart" : "Unavailable"}
                        </Button>
                      </CardFooter>
                    </div>
                  </div>
                </Card>
              </div>
            ))}

            {/* Center Pagination */}
            {pagination.totalPages > 1 && (
              <div className="px-4 py-6">
                <CenterPagination
                  currentPage={pagination.currentPage}
                  totalPages={pagination.totalPages}
                  centerPages={pagination.centerPages}
                  showFirst={pagination.showFirst}
                  showLast={pagination.showLast}
                  onPageChange={pagination.setPage}
                  canGoPrev={pagination.canGoPrev}
                  canGoNext={pagination.canGoNext}
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
