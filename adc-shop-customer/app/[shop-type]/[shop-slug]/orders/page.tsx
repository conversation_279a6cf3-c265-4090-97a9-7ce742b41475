// Orders page for a specific shop
// URL: /[shop-type]/[shop-slug]/orders

import { notFound } from 'next/navigation';
import { VALID_SHOP_TYPES, ShopType, isValidShopType, getShopTypeConfig } from '@/lib/config/shop-types';
import ShopNavigation from '@/components/ShopNavigation';

interface ShopOrdersPageProps {
  params: Promise<{
    'shop-type': string;
    'shop-slug': string;
  }>;
}

export default async function ShopOrdersPage({ params }: ShopOrdersPageProps) {
  const resolvedParams = await params;
  const shopType = resolvedParams['shop-type'] as ShopType;
  const shopSlug = resolvedParams['shop-slug'];

  // Validate shop type
  if (!isValidShopType(shopType)) {
    notFound();
  }

  // For now, we'll import and use the existing orders page component
  // In the future, we can create shop-specific order components
  const { default: OrdersPage } = await import('../../../orders/page');

  return (
    <div>
      {/* Shop Navigation */}
      <ShopNavigation
        shopType={shopType}
        shopSlug={shopSlug}
        // TODO: Fetch actual shop data and pass it here
        // shopName={shop?.name}
        // shopRating={shop?.rating}
        // shopAddress={shop?.address}
        // shopPhone={shop?.phone}
        // isOpen={shop?.is_open}
      />

      {/* Orders content */}
      <OrdersPage />
    </div>
  );
}

// Generate metadata for the page
export async function generateMetadata({ params }: ShopOrdersPageProps) {
  const resolvedParams = await params;
  const shopType = resolvedParams['shop-type'] as ShopType;
  const shopSlug = resolvedParams['shop-slug'];

  if (!isValidShopType(shopType)) {
    return {
      title: 'Orders - Shop Not Found',
      description: 'The requested shop could not be found.',
    };
  }

  const config = getShopTypeConfig(shopType);
  const shopName = shopSlug.replace(/-/g, ' ');

  return {
    title: `Orders - ${shopName} | ${config.label}`,
    description: `View your orders from ${shopName}.`,
  };
}
