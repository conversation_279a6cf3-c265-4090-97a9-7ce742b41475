"use client";

import { useState, useEffect, useMemo, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { CenterPagination, PaginationInfo } from "@/components/ui/pagination";
import { Search, MapPin, Star, Navigation } from "lucide-react";
import Header from "@/components/Header";
import { ShopFilters } from "@/lib/services/customerApiClient";
import { useFilteredPagination } from "@/lib/hooks/usePagination";
import { useGetShopsQuery, useGetShopFilterOptionsQuery } from "@/lib/store/api/customerApi";
import GoogleMapComponent from "@/components/GoogleMapReact";
import type { ShopLocation } from "@/components/GoogleMapReact";
import { geocodeAddress, reverseGeocode } from "@/lib/google-maps";
import { generateShopUrl, getDefaultShopType } from "@/lib/config/shop-types";
import Link from "next/link";
import { useRouter } from "next/navigation";

// Sample restaurant data for demonstration (Bangkok area)
// Updated to match actual database shops
const sampleRestaurants: ShopLocation[] = [
  {
    id: "550e8400-e29b-41d4-a716-446655440001",
    slug: "thai-delight",
    name: "Thai Delight Restaurant",
    description: "Authentic Thai cuisine in a cozy atmosphere with traditional recipes passed down through generations. Experience the rich flavors of Thailand in our beautifully decorated restaurant.",
    lat: 13.7990471,
    lng: 100.7253852,
    address: "123 Sukhumvit Road, Bangkok",
    rating: 4.5,
    priceRange: "$$",
    cuisineType: "Thai",
    isOpen: true,
    image: "https://images.unsplash.com/photo-1559339352-11d035aa65de?q=80&w=1974&auto=format&fit=crop",
    phone: "+66-2-123-4567",
    website: "https://thaidelight.com",
  },
  {
    id: "672023d9-8971-43fb-b09e-fe41bbb55ec7",
    slug: "weerawat-poseeya",
    name: "weerawat poseeya",
    description: "ddd",
    lat: 13.8010471,
    lng: 100.7353852,
    address: "61 Lat Phrao Road, Bangkok",
    rating: 4.0,
    priceRange: "$$",
    cuisineType: "general",
    isOpen: true,
    image: "/api/placeholder/100/100",
    phone: "+66902612533",
    website: "",
  },
];

export default function HomePage() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState("");
  const [locationQuery, setLocationQuery] = useState("");
  const [selectedCuisine, setSelectedCuisine] = useState<string>("all");
  const [selectedPriceRange, setSelectedPriceRange] = useState<string>("all");
  const [selectedRating, setSelectedRating] = useState<string>("all");
  const [mapCenter, setMapCenter] = useState<{ lat: number; lng: number } | null>(null);

  // Fetch filter options
  const { data: filterOptionsResponse } = useGetShopFilterOptionsQuery();

  // Initialize pagination with filters
  const pagination = useFilteredPagination<ShopFilters>({
    initialPage: 1,
    initialLimit: 20,
    initialFilters: {
      search: "",
      cuisine_type: undefined,
      price_range: undefined,
      min_rating: undefined,
    },
  });

  // Prepare filters for RTK Query - use useMemo to ensure stable reference
  const filters: ShopFilters = useMemo(() => {
    const currentFilters: ShopFilters = {
      page: pagination.currentPage,
      limit: pagination.limit,
      search: searchQuery || undefined,
      cuisine_type: selectedCuisine !== "all" ? [selectedCuisine] : undefined,
      price_range: selectedPriceRange !== "all" ? [selectedPriceRange] : undefined,
      min_rating: selectedRating !== "all" ? parseFloat(selectedRating) : undefined,
    };

    // Debug logging
    console.log('Current filters:', currentFilters);

    return currentFilters;
  }, [
    pagination.currentPage,
    pagination.limit,
    searchQuery,
    selectedCuisine,
    selectedPriceRange,
    selectedRating,
  ]);

  // Use RTK Query to fetch shops
  const {
    data: shopsResponse,
    error,
    isLoading,
    refetch,
    isFetching,
  } = useGetShopsQuery(filters);

  // Debug logging for API calls
  useEffect(() => {
    console.log('RTK Query state:', {
      isLoading,
      isFetching,
      error,
      dataLength: shopsResponse?.data?.shops?.length || 0,
      filters,
    });
  }, [isLoading, isFetching, error, shopsResponse, filters]);

  // Extract data from response
  const shops = useMemo(() => shopsResponse?.data?.shops || [], [shopsResponse?.data?.shops]);

  // Convert shops to map locations, use real API data or filtered sample data
  const shopLocations: ShopLocation[] = useMemo(() => {
    let dataToUse: ShopLocation[] = [];

    if (shops.length === 0) {
      // Use sample data if no API data, but apply client-side filtering
      dataToUse = sampleRestaurants;
    } else {
      // Convert API data
      dataToUse = shops.map(shop => ({
        id: shop.id,
        slug: shop.slug,
        name: shop.name,
        description: shop.description,
        lat: shop.address?.latitude || 13.8090471, // Use your location as default
        lng: shop.address?.longitude || 100.7353852,
        address: shop.address?.street && shop.address?.city
          ? `${shop.address.street}, ${shop.address.city}`
          : 'Bangkok, Thailand',
        rating: shop.rating || 4.0,
        priceRange: shop.price_range || '$$',
        cuisineType: shop.cuisine_type || 'Restaurant',
        isOpen: shop.is_open ?? true,
        image: shop.logo,
        phone: shop.phone,
        website: shop.website,
      }));
    }

    // Apply client-side filtering when using sample data
    if (shops.length === 0) {
      let filteredData = dataToUse;

      // Apply search filter
      if (searchQuery && searchQuery.trim()) {
        const query = searchQuery.toLowerCase();
        filteredData = filteredData.filter(shop =>
          shop.name.toLowerCase().includes(query) ||
          (shop.description && shop.description.toLowerCase().includes(query)) ||
          (shop.cuisineType && shop.cuisineType.toLowerCase().includes(query))
        );
      }

      // Apply cuisine filter
      if (selectedCuisine !== "all") {
        filteredData = filteredData.filter(shop =>
          shop.cuisineType === selectedCuisine
        );
      }

      // Apply price range filter
      if (selectedPriceRange !== "all") {
        filteredData = filteredData.filter(shop =>
          shop.priceRange === selectedPriceRange
        );
      }

      // Apply rating filter
      if (selectedRating !== "all") {
        const minRating = parseFloat(selectedRating);
        filteredData = filteredData.filter(shop =>
          shop.rating && shop.rating >= minRating
        );
      }

      console.log('Client-side filtering applied:', {
        originalCount: dataToUse.length,
        filteredCount: filteredData.length,
        searchQuery,
        selectedCuisine,
        selectedPriceRange,
        selectedRating,
      });

      return filteredData;
    }

    return dataToUse;
  }, [shops, searchQuery, selectedCuisine, selectedPriceRange, selectedRating]);

  // Update pagination info when data changes
  useEffect(() => {
    if (shopsResponse?.pagination) {
      pagination.setPaginationInfo(shopsResponse.pagination);
    }
  }, [shopsResponse?.pagination, pagination]);

  // Get user location on component mount
  useEffect(() => {
    const getUserLocation = async () => {
      try {
        const position = await new Promise<GeolocationPosition>((resolve, reject) => {
          navigator.geolocation.getCurrentPosition(resolve, reject);
        });
        const location = {
          lat: position.coords.latitude,
          lng: position.coords.longitude,
        };
        setMapCenter(location);
      } catch (error) {
        console.warn('Could not get user location:', error);
        // Default to Bangkok, Thailand
        const defaultLocation = { lat: 13.7563, lng: 100.5018 };
        setMapCenter(defaultLocation);
      }
    };

    getUserLocation();
  }, []);

  // Handle search with debouncing
  const handleSearch = useCallback((query: string) => {
    console.log('Search query changed:', query);
    setSearchQuery(query);
    pagination.setPage(1);
  }, [pagination]);

  // Handle location search with geocoding
  const handleLocationSearch = async (query: string) => {
    setLocationQuery(query);
    if (query.trim()) {
      try {
        const location = await geocodeAddress(query);
        if (location) {
          const newCenter = { lat: location.lat(), lng: location.lng() };
          setMapCenter(newCenter);
        }
      } catch (error) {
        console.warn('Geocoding failed:', error);
      }
    }
  };

  // Handle shop selection from map
  const handleShopClick = (shop: ShopLocation) => {
    // Navigate to shop detail page using Next.js router
    // Default to 'food' shop type for now, but this could be dynamic based on shop data
    const shopType = getDefaultShopType(); // This could come from shop.type or shop.category in the future
    const url = generateShopUrl(shopType, shop.slug || shop.id);
    router.push(url);
  };

  // Handle map click for location selection with reverse geocoding
  const handleMapClick = async (lat: number, lng: number) => {
    setMapCenter({ lat, lng });
    try {
      const address = await reverseGeocode(lat, lng);
      if (address) {
        setLocationQuery(address);
      }
    } catch (error) {
      console.warn('Reverse geocoding failed:', error);
    }
  };

  // Handle "Find My Location" button
  const handleFindMyLocation = async () => {
    try {
      const position = await new Promise<GeolocationPosition>((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(resolve, reject);
      });
      const location = {
        lat: position.coords.latitude,
        lng: position.coords.longitude,
      };
      setMapCenter(location);
    } catch (error) {
      console.error('Could not get user location:', error);
      alert('Unable to get your location. Please check your browser permissions.');
    }
  };

  // Filter handlers
  const handleCuisineChange = useCallback((value: string) => {
    console.log('Cuisine filter changed:', value);
    setSelectedCuisine(value);
    pagination.setPage(1);
  }, [pagination]);

  const handlePriceRangeChange = useCallback((value: string) => {
    console.log('Price range filter changed:', value);
    setSelectedPriceRange(value);
    pagination.setPage(1);
  }, [pagination]);

  const handleRatingChange = useCallback((value: string) => {
    console.log('Rating filter changed:', value);
    setSelectedRating(value);
    pagination.setPage(1);
  }, [pagination]);

  // Extract filter options from API response
  const filterOptions = useMemo(() => {
    const options = filterOptionsResponse?.data;
    return {
      cuisineTypes: options?.cuisine_types || [
        "Italian", "Chinese", "Japanese", "Mexican", "Indian",
        "Thai", "American", "French", "Mediterranean", "Korean"
      ],
      priceRanges: options?.price_ranges || ["$", "$$", "$$$", "$$$$"],
      features: options?.features || []
    };
  }, [filterOptionsResponse]);

  if (isLoading && shops.length === 0) {
    return (
      <div className="relative flex size-full min-h-screen flex-col bg-background">
        <div className="layout-container flex h-full grow flex-col">
          <Header />
          <div className="flex flex-1 justify-center items-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">Loading restaurants...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    const errorMessage = 'status' in error
      ? `Error ${error.status}: Failed to load restaurants`
      : error.message || 'Failed to load restaurants';

    return (
      <div className="relative flex size-full min-h-screen flex-col bg-background">
        <div className="layout-container flex h-full grow flex-col">
          <Header />
          <div className="flex flex-1 justify-center items-center">
            <div className="text-center">
              <p className="text-destructive mb-4">{errorMessage}</p>
              <Button onClick={() => refetch()}>Try Again</Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative flex size-full min-h-screen flex-col bg-background">
      <div className="layout-container flex h-full grow flex-col">
        <Header />

        {/* Main Content */}
        <div className="px-40 flex flex-1 justify-center py-5">
          <div className="layout-content-container flex flex-col max-w-[960px] flex-1">

            {/* Hero Section with Search */}
            <div className="px-4 py-8">
              <h1 className="text-foreground tracking-light text-[32px] font-bold leading-tight text-center mb-8">
                Discover Restaurants Near You
              </h1>

              {/* Search Bars */}
              <div className="space-y-4 mb-6">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                  <Input
                    placeholder="Search for restaurants or cuisines"
                    value={searchQuery}
                    onChange={(e) => handleSearch(e.target.value)}
                    className="pl-10 h-12"
                  />
                </div>

                <div className="relative">
                  <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                  <Input
                    placeholder="Enter an address"
                    value={locationQuery}
                    onChange={(e) => handleLocationSearch(e.target.value)}
                    className="pl-10 h-12"
                  />
                </div>
              </div>

              {/* Interactive Google Map */}
              <div className="w-full h-64 rounded-lg mb-6 relative overflow-hidden">
                <GoogleMapComponent
                  shops={shopLocations}
                  center={mapCenter || undefined}
                  zoom={15}
                  height="256px"
                  width="100%"
                  showUserLocation={true}
                  onShopClick={handleShopClick}
                  onMapClick={handleMapClick}
                  className="rounded-lg"
                />
                {/* Find My Location Button */}
                <div className="absolute top-4 right-4">
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={handleFindMyLocation}
                    className="bg-white/90 hover:bg-white shadow-md"
                    title="Find my location"
                  >
                    <Navigation className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

            <h2 className="text-foreground tracking-light text-[28px] font-bold leading-tight px-4 text-left pb-3">
              Restaurants Near You
            </h2>

            {/* Filter Dropdowns */}
            <div className="flex gap-3 p-4 flex-wrap">
              <div className="w-[180px]">
                <Select value={selectedCuisine} onValueChange={handleCuisineChange}>
                  <SelectTrigger className="h-10">
                    <SelectValue placeholder="Cuisine" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Cuisines</SelectItem>
                    {filterOptions.cuisineTypes.map((cuisine) => (
                      <SelectItem key={cuisine} value={cuisine}>
                        {cuisine}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="w-[180px]">
                <Select value={selectedPriceRange} onValueChange={handlePriceRangeChange}>
                  <SelectTrigger className="h-10">
                    <SelectValue placeholder="Price Range" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Prices</SelectItem>
                    {filterOptions.priceRanges.map((range) => (
                      <SelectItem key={range} value={range}>
                        {range}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="w-[180px]">
                <Select value={selectedRating} onValueChange={handleRatingChange}>
                  <SelectTrigger className="h-10">
                    <SelectValue placeholder="Ratings" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Ratings</SelectItem>
                    <SelectItem value="4.5">4.5+ Stars</SelectItem>
                    <SelectItem value="4.0">4.0+ Stars</SelectItem>
                    <SelectItem value="3.5">3.5+ Stars</SelectItem>
                    <SelectItem value="3.0">3.0+ Stars</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Pagination Info */}
            <div className="px-4 pb-3">
              <PaginationInfo
                startItem={pagination.startItem}
                endItem={pagination.endItem}
                totalItems={pagination.totalItems}
              />
            </div>

            {/* Restaurant List */}
            {shopLocations.map((shop) => (
              <div key={shop.id} className="p-4">
                <Link href={generateShopUrl(getDefaultShopType(), shop.slug || shop.id)}>
                  <Card className="overflow-hidden hover:shadow-lg transition-shadow cursor-pointer">
                    <div className="flex items-center gap-4 p-4">
                      <div
                        className="w-16 h-16 bg-center bg-no-repeat bg-cover rounded-lg flex-shrink-0"
                        style={{
                          backgroundImage: shop.image ? `url("${shop.image}")` : 'none',
                          backgroundColor: shop.image ? 'transparent' : '#e5ccb2'
                        }}
                      />
                      <div className="flex-1 min-w-0">
                        <CardHeader className="p-0">
                          <CardTitle className="text-lg font-semibold text-foreground">
                            {shop.name}
                          </CardTitle>
                          <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <div className="flex items-center gap-1">
                              <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                              <span>{shop.rating || "4.5"}</span>
                            </div>
                            <span>•</span>
                            <span>{shop.cuisineType || "Italian"}</span>
                            <span>•</span>
                            <span>{shop.priceRange || "$$"}</span>
                          </div>
                          {shop.description && (
                            <CardDescription className="text-sm mt-1">
                              {shop.description}
                            </CardDescription>
                          )}
                        </CardHeader>
                      </div>
                    </div>
                  </Card>
                </Link>
              </div>
            ))}

            {/* Center Pagination */}
            {pagination.totalPages > 1 && (
              <div className="px-4 py-6">
                <CenterPagination
                  currentPage={pagination.currentPage}
                  totalPages={pagination.totalPages}
                  centerPages={pagination.centerPages}
                  showFirst={pagination.showFirst}
                  showLast={pagination.showLast}
                  onPageChange={pagination.setPage}
                  canGoPrev={pagination.canGoPrev}
                  canGoNext={pagination.canGoNext}
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
