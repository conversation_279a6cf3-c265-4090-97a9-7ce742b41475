"use client";

import { usePathname } from "next/navigation";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ShoppingBag, Clock, MapPin, Phone, Star } from "lucide-react";
import { generateShopOrdersUrl, getShopTypeConfig, ShopType } from "@/lib/config/shop-types";

interface ShopNavigationProps {
  shopType: ShopType;
  shopSlug: string;
  shopName?: string;
  shopRating?: number;
  shopAddress?: string;
  shopPhone?: string;
  isOpen?: boolean;
  className?: string;
}

export default function ShopNavigation({
  shopType,
  shopSlug,
  shopName,
  shopRating,
  shopAddress,
  shopPhone,
  isOpen,
  className = "",
}: ShopNavigationProps) {
  const pathname = usePathname();
  const config = getShopTypeConfig(shopType);
  
  // Determine if we're on the orders page
  const isOrdersPage = pathname.includes('/orders');
  
  // Generate URLs
  const shopUrl = `/${shopType}/${shopSlug}`;
  const ordersUrl = generateShopOrdersUrl(shopType, shopSlug);

  return (
    <div className={`bg-white border-b border-gray-200 ${className}`}>
      {/* Shop Header */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          {/* Shop Info */}
          <div className="flex-1">
            <div className="flex items-center gap-2 text-sm text-gray-600 mb-2">
              <span className="capitalize">{config.label}</span>
              <span>/</span>
              <span className="font-medium text-gray-900">
                {shopName || shopSlug.replace(/-/g, ' ')}
              </span>
            </div>
            
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              {shopName || shopSlug.replace(/-/g, ' ')}
            </h1>
            
            <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600">
              {shopRating && (
                <div className="flex items-center gap-1">
                  <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  <span className="font-medium">{shopRating.toFixed(1)}</span>
                </div>
              )}
              
              {isOpen !== undefined && (
                <div className="flex items-center gap-1">
                  <Clock className="h-4 w-4" />
                  <Badge variant={isOpen ? "default" : "secondary"} className="text-xs">
                    {isOpen ? 'Open' : 'Closed'}
                  </Badge>
                </div>
              )}
              
              {shopAddress && (
                <div className="flex items-center gap-1">
                  <MapPin className="h-4 w-4" />
                  <span className="truncate max-w-xs">{shopAddress}</span>
                </div>
              )}
              
              {shopPhone && (
                <div className="flex items-center gap-1">
                  <Phone className="h-4 w-4" />
                  <span>{shopPhone}</span>
                </div>
              )}
            </div>
          </div>
          
          {/* Action Buttons */}
          <div className="flex items-center gap-2">
            <Button
              variant={isOrdersPage ? "default" : "outline"}
              size="sm"
              asChild
            >
              <Link href={ordersUrl} className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                My Orders
              </Link>
            </Button>
            
            <Button
              variant={!isOrdersPage ? "default" : "outline"}
              size="sm"
              asChild
            >
              <Link href={shopUrl} className="flex items-center gap-2">
                <ShoppingBag className="h-4 w-4" />
                Menu
              </Link>
            </Button>
          </div>
        </div>
      </div>
      
      {/* Navigation Tabs */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <nav className="flex space-x-8 border-b border-gray-200">
          <Link
            href={shopUrl}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              !isOrdersPage
                ? 'border-orange-500 text-orange-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Menu & Info
          </Link>
          
          <Link
            href={ordersUrl}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              isOrdersPage
                ? 'border-orange-500 text-orange-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            My Orders
          </Link>
        </nav>
      </div>
    </div>
  );
}
